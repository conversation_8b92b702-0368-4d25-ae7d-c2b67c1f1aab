#!/usr/bin/env python3
"""
Simple card cropper - Easy-to-use function for cropping cards from images.
"""

from detect_and_crop_cards import crop_cards_from_image
import os

def crop_cards(input_image_path, output_directory="cropped_cards"):
    """
    Simple function to crop cards from an image.
    
    Args:
        input_image_path (str): Path to the input image containing cards
        output_directory (str): Directory where cropped cards will be saved
        
    Returns:
        list: List of paths to the cropped card images
        
    Example:
        # Crop cards from an image
        card_paths = crop_cards("my_image.jpg", "output_folder")
        
        # Print results
        if card_paths:
            print(f"Successfully cropped {len(card_paths)} cards:")
            for i, path in enumerate(card_paths, 1):
                print(f"  Card {i}: {path}")
        else:
            print("No cards detected in the image.")
    """
    
    if not os.path.exists(input_image_path):
        raise FileNotFoundError(f"Input image not found: {input_image_path}")
    
    try:
        return crop_cards_from_image(input_image_path, output_directory)
    except Exception as e:
        print(f"Error processing image: {str(e)}")
        return []

# Example usage
if __name__ == "__main__":
    # Example: Process test4.jpg
    test_image = os.path.join("test images", "test4.jpg")
    
    if os.path.exists(test_image):
        print("Running example with test4.jpg...")
        card_paths = crop_cards(test_image)
        
        if card_paths:
            print(f"\n✅ Successfully cropped {len(card_paths)} cards:")
            for i, path in enumerate(card_paths, 1):
                print(f"  Card {i}: {path}")
        else:
            print("❌ No cards detected in the image.")
    else:
        print("Test image not found. Please provide an image path.")
        print("\nUsage example:")
        print("  from simple_card_cropper import crop_cards")
        print("  card_paths = crop_cards('path/to/your/image.jpg')")
