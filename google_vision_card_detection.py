"""
Google Vision API Card Detection
Reliable card detection using Google Cloud Vision API
"""

import os
import cv2
import numpy as np
from google.cloud import vision
import io
from typing import List, <PERSON><PERSON>

def setup_google_vision():
    """
    Setup instructions for Google Vision API
    """
    print("🔧 GOOGLE VISION API SETUP")
    print("=" * 50)
    print("1. Go to: https://console.cloud.google.com/")
    print("2. Create a new project or select existing one")
    print("3. Enable the Vision API")
    print("4. Create a service account key (JSON)")
    print("5. Download the JSON key file")
    print("6. Set environment variable:")
    print("   export GOOGLE_APPLICATION_CREDENTIALS='path/to/your/key.json'")
    print("7. Install the library:")
    print("   pip install google-cloud-vision")
    print("\n" + "=" * 50)

def detect_cards_with_vision(image_path="test images/test4.jpg", 
                           expand_top=50, expand_bottom=20, 
                           expand_left=20, expand_right=20,
                           debugging=True):
    """
    Detect cards using Google Vision API document detection
    
    Args:
        image_path: Path to image
        expand_top/bottom/left/right: Expansion percentages
        debugging: Print debug info
    
    Returns:
        List of cropped card paths
    """
    
    if debugging:
        print(f"🔍 Processing with Google Vision: {image_path}")
    
    # Check if credentials are set
    if not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
        print("❌ Google Vision API credentials not found!")
        print("Please set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        setup_google_vision()
        return []
    
    try:
        # Initialize the client
        client = vision.ImageAnnotatorClient()
        
        # Load image
        with io.open(image_path, 'rb') as image_file:
            content = image_file.read()
        
        image = vision.Image(content=content)
        
        # Detect documents (cards are document-like objects)
        response = client.document_text_detection(image=image)
        
        if response.error.message:
            raise Exception(f'Google Vision API error: {response.error.message}')
        
        # Get document bounds
        document = response.full_text_annotation
        
        if not document.pages:
            if debugging:
                print("❌ No document pages detected")
            return []
        
        # Load original image for cropping
        original_image = cv2.imread(image_path)
        height, width = original_image.shape[:2]
        
        if debugging:
            print(f"📏 Image dimensions: {width}x{height}")
            print(f"📄 Found {len(document.pages)} document pages")
        
        saved_paths = []
        os.makedirs("vision_cropped_cards", exist_ok=True)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        for i, page in enumerate(document.pages):
            if debugging:
                print(f"\n🔧 Processing page {i+1}...")
            
            # Get page dimensions and blocks
            page_width = page.width
            page_height = page.height
            
            if debugging:
                print(f"  Page dimensions: {page_width}x{page_height}")
                print(f"  Found {len(page.blocks)} blocks")
            
            # Find the largest block (likely the main card content)
            if page.blocks:
                largest_block = max(page.blocks, key=lambda b: len(b.bounding_box.vertices))
                
                # Get bounding box vertices
                vertices = largest_block.bounding_box.vertices
                
                if len(vertices) >= 4:
                    # Convert normalized coordinates to pixel coordinates
                    points = []
                    for vertex in vertices[:4]:
                        x = int(vertex.x * width / page_width) if hasattr(vertex, 'x') else vertex.x
                        y = int(vertex.y * height / page_height) if hasattr(vertex, 'y') else vertex.y
                        points.append([x, y])
                    
                    if debugging:
                        print(f"  Block vertices: {points}")
                    
                    # Calculate bounding rectangle
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    
                    min_x, max_x = min(x_coords), max(x_coords)
                    min_y, max_y = min(y_coords), max(y_coords)
                    
                    # Apply expansion
                    card_width = max_x - min_x
                    card_height = max_y - min_y
                    
                    expand_top_px = int((expand_top / 100.0) * card_height)
                    expand_bottom_px = int((expand_bottom / 100.0) * card_height)
                    expand_left_px = int((expand_left / 100.0) * card_width)
                    expand_right_px = int((expand_right / 100.0) * card_width)
                    
                    # Calculate expanded bounds
                    crop_x1 = max(0, min_x - expand_left_px)
                    crop_y1 = max(0, min_y - expand_top_px)
                    crop_x2 = min(width, max_x + expand_right_px)
                    crop_y2 = min(height, max_y + expand_bottom_px)
                    
                    if debugging:
                        print(f"  Original bounds: ({min_x}, {min_y}) to ({max_x}, {max_y})")
                        print(f"  Expanded bounds: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")
                        print(f"  Expansion: top={expand_top_px}px, bottom={expand_bottom_px}px, left={expand_left_px}px, right={expand_right_px}px")
                    
                    # Crop the card
                    cropped_card = original_image[crop_y1:crop_y2, crop_x1:crop_x2]
                    
                    if cropped_card.size > 0:
                        output_path = f"vision_cropped_cards/{base_name}_vision_card_{i+1}.jpg"
                        cv2.imwrite(output_path, cropped_card)
                        saved_paths.append(output_path)
                        
                        if debugging:
                            print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
                    else:
                        if debugging:
                            print(f"  ❌ Empty crop result")
        
        return saved_paths
        
    except Exception as e:
        print(f"❌ Error with Google Vision API: {str(e)}")
        if "google.cloud" in str(e):
            print("💡 Make sure you have installed: pip install google-cloud-vision")
        return []

def test_vision_detection():
    """Test Google Vision detection"""
    
    print("🤖 GOOGLE VISION CARD DETECTION TEST")
    print("=" * 50)
    
    card_paths = detect_cards_with_vision("test images/test4.jpg", debugging=True)
    
    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
        
        print(f"\n📁 Cards saved to: vision_cropped_cards/")
        
    else:
        print("❌ No cards detected or API not configured")
    
    return card_paths

if __name__ == "__main__":
    test_vision_detection()
