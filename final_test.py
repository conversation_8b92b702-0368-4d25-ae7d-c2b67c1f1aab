#!/usr/bin/env python3
"""
Final test with recommended settings for complete card capture.
"""

import os
from simple_card_cropper import crop_cards

def final_test():
    """Test with recommended settings for complete card capture"""
    
    test_image = os.path.join("test images", "test4.jpg")
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    print("🎯 Final Test: Complete Card Capture")
    print("=" * 50)
    print(f"Input image: {test_image}")
    
    # Recommended settings for complete card capture including person photos
    print("\n📋 Using recommended expansion settings:")
    print("  - expand_top: 120% (to capture person photos)")
    print("  - expand_bottom: 40% (to capture bottom details)")
    print("  - expand_left: 40% (to capture left side)")
    print("  - expand_right: 40% (to capture right side)")
    
    card_paths = crop_cards(
        test_image, 
        "final_complete_cards",
        expand_top=120,    # High expansion upward for person photos
        expand_bottom=40,  # Moderate expansion downward
        expand_left=40,    # Moderate expansion to left
        expand_right=40    # Moderate expansion to right
    )
    
    if card_paths:
        print(f"\n✅ SUCCESS! Cropped {len(card_paths)} complete cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024  # Size in KB
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
            else:
                print(f"  ❌ Card {i}: {path} (FILE NOT FOUND)")
        
        print(f"\n📁 Output directory: final_complete_cards/")
        print("🔍 Check the cropped images to verify they include:")
        print("   ✓ Person's photograph")
        print("   ✓ All text and details")
        print("   ✓ Complete card boundaries")
        print("   ✓ Father's name and family information")
        
    else:
        print("❌ No cards were detected or cropped.")
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")

if __name__ == "__main__":
    final_test()
