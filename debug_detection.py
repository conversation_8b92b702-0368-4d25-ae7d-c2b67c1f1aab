#!/usr/bin/env python3
"""
Debug script to analyze the image and improve detection.
"""

import cv2
import numpy as np
import os

def debug_image_analysis(image_path):
    """Analyze the image to understand why detection failed"""
    
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Could not load image from {image_path}")
        return
    
    print(f"Image shape: {image.shape}")
    print(f"Image dimensions: {image.shape[1]}x{image.shape[0]}")
    
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Try different edge detection approaches
    print("\nTrying different edge detection methods...")
    
    # Method 1: Simple Canny
    edges1 = cv2.Canny(gray, 50, 150)
    contours1, _ = cv2.findContours(edges1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Method 1 (Canny 50,150): Found {len(contours1)} contours")
    
    # Method 2: Lower threshold Canny
    edges2 = cv2.Canny(gray, 30, 100)
    contours2, _ = cv2.findContours(edges2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Method 2 (Canny 30,100): Found {len(contours2)} contours")
    
    # Method 3: Adaptive threshold + Canny
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    edges3 = cv2.Canny(thresh, 50, 150)
    contours3, _ = cv2.findContours(edges3, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"Method 3 (Adaptive + Canny): Found {len(contours3)} contours")
    
    # Analyze contour areas
    height, width = image.shape[:2]
    total_area = width * height
    
    print(f"\nImage total area: {total_area}")
    print(f"Looking for contours between {total_area * 0.05:.0f} and {total_area * 0.8:.0f}")
    
    # Check largest contours from each method
    for method_num, contours in enumerate([contours1, contours2, contours3], 1):
        if contours:
            # Sort by area
            contours_sorted = sorted(contours, key=cv2.contourArea, reverse=True)
            print(f"\nMethod {method_num} - Top 5 contour areas:")
            for i, contour in enumerate(contours_sorted[:5]):
                area = cv2.contourArea(contour)
                percentage = (area / total_area) * 100
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = float(w) / h
                print(f"  {i+1}: Area={area:.0f} ({percentage:.2f}%), AspectRatio={aspect_ratio:.2f}, BBox=({w}x{h})")
    
    # Save debug images
    debug_dir = "debug_images"
    os.makedirs(debug_dir, exist_ok=True)
    
    cv2.imwrite(os.path.join(debug_dir, "edges1_canny_50_150.jpg"), edges1)
    cv2.imwrite(os.path.join(debug_dir, "edges2_canny_30_100.jpg"), edges2)
    cv2.imwrite(os.path.join(debug_dir, "edges3_adaptive_canny.jpg"), edges3)
    
    print(f"\nDebug images saved to {debug_dir}/")

if __name__ == "__main__":
    test_image = os.path.join("test images", "test4.jpg")
    debug_image_analysis(test_image)
