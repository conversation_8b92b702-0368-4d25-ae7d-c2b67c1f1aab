# Automatic Card Detection and Cropping

A reliable Python solution for automatically detecting and cropping cards from images using computer vision techniques.

## Features

- **Automatic Detection**: Detects multiple cards in a single image
- **Complete Card Cropping**: Crops the entire card including photos, text, and all details within card boundaries
- **Perspective Correction**: Automatically corrects perspective distortion to produce clean rectangular card images
- **Robust Algorithm**: Uses OpenCV with edge detection, contour analysis, and perspective transformation
- **Easy Integration**: Simple function interface for easy integration into other projects
- **Command Line Interface**: Can be used directly from command line

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install opencv-python numpy
```

## Usage

### Simple Function Interface

```python
from simple_card_cropper import crop_cards

# Crop cards from an image
card_paths = crop_cards("path/to/your/image.jpg", "output_folder")

if card_paths:
    print(f"Successfully cropped {len(card_paths)} cards:")
    for i, path in enumerate(card_paths, 1):
        print(f"  Card {i}: {path}")
else:
    print("No cards detected in the image.")
```

### Command Line Interface

```bash
# Basic usage
python detect_and_crop_cards.py "path/to/image.jpg"

# Specify output directory
python detect_and_crop_cards.py "path/to/image.jpg" --output-dir "my_output_folder"
```

### Advanced Usage

```python
from detect_and_crop_cards import crop_cards_from_image

# More control over the process
saved_paths = crop_cards_from_image("image.jpg", "output_dir")
```

## How It Works

1. **Preprocessing**: Converts image to grayscale and applies noise reduction
2. **Edge Detection**: Uses Canny edge detection to find card boundaries
3. **Contour Analysis**: Identifies rectangular contours that match card characteristics
4. **Filtering**: Filters contours based on area (2-80% of image) and aspect ratio (0.4-3.0)
5. **Perspective Correction**: Applies perspective transformation to get clean rectangular crops
6. **Output**: Saves each detected card as a separate image file

## Output

- Cards are saved as separate JPEG files
- Naming convention: `{original_filename}_card_{number}.jpg`
- Cards include the complete card boundary with all content (photos, text, tables, etc.)
- Perspective distortion is automatically corrected

## Example Results

For an image containing 2 family cards:
- `test4_card_1.jpg` - First detected card
- `test4_card_2.jpg` - Second detected card

Each cropped card will contain:
- Person's photograph
- All text and details
- Complete card boundary
- Properly oriented rectangular format

## Technical Details

- **Library**: OpenCV (cv2) for computer vision operations
- **Algorithm**: Edge detection + contour analysis + perspective transformation
- **Supported Formats**: Common image formats (JPEG, PNG, etc.)
- **Performance**: Optimized for accuracy and reliability

## Files

- `detect_and_crop_cards.py` - Main detection and cropping implementation
- `simple_card_cropper.py` - Simple interface for easy integration
- `test_card_detection.py` - Test script
- `debug_detection.py` - Debug analysis tool
- `requirements.txt` - Python dependencies

## Requirements

- Python 3.7+
- OpenCV (opencv-python)
- NumPy

## Troubleshooting

If cards are not detected:
1. Ensure cards have clear boundaries and good contrast
2. Check that cards occupy at least 2% of the image area
3. Verify image quality and lighting
4. Use the debug script to analyze detection parameters

## License

This project is provided as-is for educational and practical use.
