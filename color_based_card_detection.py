import cv2
import numpy as np
import os

def crop_cards_by_color(image_path="test images/test1.jpg", 
                       expand_top=120, expand_bottom=40, expand_left=40, expand_right=40,
                       debugging=True):
    """
    Detect and crop cards based on green color instead of table detection.
    
    Args:
        image_path: Path to image (default: "test images/test4.jpg")
        expand_top: % to expand upward (default: 120)
        expand_bottom: % to expand downward (default: 40) 
        expand_left: % to expand left (default: 40)
        expand_right: % to expand right (default: 40)
        debugging: Print debug information (default: True)
    
    Returns:
        List of cropped card image paths
    """
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Could not load image: {image_path}")
        return []
    
    if debugging:
        print(f"🔍 Processing: {image_path}")
        print(f"📏 Image dimensions: {image.shape[1]}x{image.shape[0]}")
    
    # Convert BGR to HSV for better color detection
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Convert the specific hex colors to HSV for precise detection
    # c4deca = RGB(196, 222, 202)
    c4deca_bgr = np.uint8([[[202, 222, 196]]])  # BGR format for OpenCV
    c4deca_hsv = cv2.cvtColor(c4deca_bgr, cv2.COLOR_BGR2HSV)[0][0]

    # d8dcd5 = RGB(216, 220, 213)
    d8dcd5_bgr = np.uint8([[[213, 220, 216]]])  # BGR format for OpenCV
    d8dcd5_hsv = cv2.cvtColor(d8dcd5_bgr, cv2.COLOR_BGR2HSV)[0][0]

    if debugging:
        print(f"🎯 Target colors in HSV:")
        print(f"  c4deca: {c4deca_hsv}")
        print(f"  d8dcd5: {d8dcd5_hsv}")

    # Create multiple green ranges based on actual colors found in test4.jpg
    green_ranges = [
        # Range around the actual green colors found (H=30, S=2, V=205-232)
        ([25, 0, 200], [35, 10, 240]),    # Tight range around actual colors
        # Range around c4deca (H=67, S=30, V=222)
        ([60, 20, 210], [75, 40, 230]),   # Range around c4deca
        # Range around d8dcd5 (H=47, S=8, V=220)
        ([40, 0, 210], [55, 20, 230]),    # Range around d8dcd5
        # Broader light green range
        ([25, 0, 180], [80, 30, 255]),    # Broader range for variations
        # Very broad green range for safety
        ([20, 0, 150], [100, 50, 255]),   # Very broad green detection
    ]
    
    if debugging:
        print(f"🎨 Detecting green colors with {len(green_ranges)} different ranges...")
    
    # Create combined mask for all green ranges
    combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    for i, (lower, upper) in enumerate(green_ranges):
        lower_bound = np.array(lower, dtype=np.uint8)
        upper_bound = np.array(upper, dtype=np.uint8)
        mask = cv2.inRange(hsv, lower_bound, upper_bound)
        combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        if debugging:
            green_pixels = cv2.countNonZero(mask)
            print(f"  Range {i+1}: {green_pixels} green pixels detected")
    
    total_green_pixels = cv2.countNonZero(combined_mask)
    if debugging:
        print(f"🟢 Total green pixels: {total_green_pixels}")
    
    # Clean up the mask with more aggressive morphological operations
    # Use larger kernels to connect fragmented green areas
    kernel_small = np.ones((3, 3), np.uint8)
    kernel_medium = np.ones((7, 7), np.uint8)
    kernel_large = np.ones((15, 15), np.uint8)

    # First, close small gaps
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_small)
    # Then, close larger gaps to connect card areas
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_medium)
    # Finally, close very large gaps to form complete card shapes
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_large)
    # Remove small noise
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel_small)

    if debugging:
        cleaned_green_pixels = cv2.countNonZero(combined_mask)
        print(f"🧹 After cleanup: {cleaned_green_pixels} green pixels")
    
    # Save debug images if debugging is enabled
    if debugging:
        os.makedirs("debug_color", exist_ok=True)
        cv2.imwrite("debug_color/green_mask.jpg", combined_mask)
        
        # Create colored overlay for visualization
        green_overlay = image.copy()
        green_overlay[combined_mask > 0] = [0, 255, 0]  # Highlight detected green areas
        blended = cv2.addWeighted(image, 0.7, green_overlay, 0.3, 0)
        cv2.imwrite("debug_color/green_detection_overlay.jpg", blended)
        print("💾 Debug images saved to debug_color/")
    
    # Find contours in the green mask
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if debugging:
        print(f"🔍 Found {len(contours)} contours in green mask")
    
    # Filter contours for card-like shapes
    height, width = image.shape[:2]
    min_area = (width * height) * 0.001  # At least 0.1% of image (very low threshold)
    max_area = (width * height) * 0.8    # At most 80% of image

    if debugging:
        print(f"📐 Area thresholds: min={min_area:.0f} ({0.1:.1f}%), max={max_area:.0f} ({80:.1f}%)")
    
    card_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area > min_area and area < max_area:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h
            
            if debugging:
                print(f"  Contour {i}: Area={area:.0f} ({(area/(width*height)*100):.2f}%), "
                      f"AspectRatio={aspect_ratio:.2f}, BBox=({w}x{h})")
            
            # More lenient aspect ratio for color-based detection
            if 0.3 <= aspect_ratio <= 4.0:  # Wider range than table detection
                card_contours.append(contour)
                if debugging:
                    print(f"    ✅ Accepted as card candidate")
            else:
                if debugging:
                    print(f"    ❌ Rejected: aspect ratio out of range")
        else:
            if debugging and area > 1000:  # Only show debug for reasonably sized contours
                print(f"  Contour {i}: Area={area:.0f} - too {'small' if area < min_area else 'large'}")
    
    # Sort by area and take top candidates
    card_contours.sort(key=cv2.contourArea, reverse=True)
    card_contours = card_contours[:3]  # Take top 3 instead of 2 for more flexibility

    # If no large contours found, try to group smaller contours that might be parts of cards
    if not card_contours and len(contours) > 0:
        if debugging:
            print("🔄 No large contours found, trying to group smaller green areas...")

        # Get all contours with reasonable size (even if below main threshold)
        small_min_area = (width * height) * 0.0001  # 0.01% of image
        small_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > small_min_area:
                small_contours.append(contour)

        if debugging:
            print(f"  Found {len(small_contours)} smaller green areas")

        # Group contours by proximity (simple approach)
        if small_contours:
            # Take the largest small contours as potential card parts
            small_contours.sort(key=cv2.contourArea, reverse=True)
            card_contours = small_contours[:2]  # Take top 2 small contours

            if debugging:
                for i, contour in enumerate(card_contours):
                    area = cv2.contourArea(contour)
                    print(f"  Using small contour {i+1}: area={area:.0f}")

    if not card_contours:
        print("❌ No card-like green areas detected")
        if debugging:
            print("💡 Try adjusting the green color ranges or check if the image contains green elements")
        return []
    
    if debugging:
        print(f"🎯 Selected {len(card_contours)} card candidates for processing")
    
    # Create output directory
    os.makedirs("color_cropped_cards", exist_ok=True)
    
    saved_paths = []
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    
    for i, contour in enumerate(card_contours):
        if debugging:
            print(f"\n🔧 Processing card {i+1}...")
        
        # Get corner points - use bounding rectangle for more reliable results with color detection
        x, y, w, h = cv2.boundingRect(contour)
        
        # Create corners from bounding rectangle
        corners = np.array([
            [x, y],           # top-left
            [x + w, y],       # top-right  
            [x + w, y + h],   # bottom-right
            [x, y + h]        # bottom-left
        ], dtype=np.float32)
        
        if debugging:
            print(f"  Original bounding box: ({x}, {y}) to ({x+w}, {y+h})")
        
        # Calculate expansion
        current_width = w
        current_height = h
        
        expand_top_px = (expand_top / 100.0) * current_height
        expand_bottom_px = (expand_bottom / 100.0) * current_height
        expand_left_px = (expand_left / 100.0) * current_width
        expand_right_px = (expand_right / 100.0) * current_width
        
        if debugging:
            print(f"  Expansion: top={expand_top_px:.0f}px, bottom={expand_bottom_px:.0f}px, "
                  f"left={expand_left_px:.0f}px, right={expand_right_px:.0f}px")
        
        # Expand corners
        new_x1 = max(0, x - expand_left_px)
        new_y1 = max(0, y - expand_top_px)
        new_x2 = min(width-1, x + w + expand_right_px)
        new_y2 = min(height-1, y + h + expand_bottom_px)
        
        expanded_corners = np.array([
            [new_x1, new_y1],     # top-left
            [new_x2, new_y1],     # top-right
            [new_x2, new_y2],     # bottom-right
            [new_x1, new_y2]      # bottom-left
        ], dtype=np.float32)
        
        if debugging:
            print(f"  Expanded bounding box: ({new_x1:.0f}, {new_y1:.0f}) to ({new_x2:.0f}, {new_y2:.0f})")
        
        # Calculate output dimensions
        final_width = int(new_x2 - new_x1)
        final_height = int(new_y2 - new_y1)
        
        # Simple crop instead of perspective transform for more reliable results
        crop_x1, crop_y1 = int(new_x1), int(new_y1)
        crop_x2, crop_y2 = int(new_x2), int(new_y2)
        
        cropped_card = image[crop_y1:crop_y2, crop_x1:crop_x2]
        
        if cropped_card.size == 0:
            if debugging:
                print(f"  ❌ Empty crop result, skipping card {i+1}")
            continue
        
        # Save
        output_path = f"color_cropped_cards/{base_name}_color_card_{i+1}.jpg"
        cv2.imwrite(output_path, cropped_card)
        saved_paths.append(output_path)
        
        if debugging:
            print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
    
    return saved_paths

def analyze_image_colors(image_path, debugging=True):
    """Analyze what colors are actually present in the image"""

    image = cv2.imread(image_path)
    if image is None:
        return

    if debugging:
        print(f"🎨 Analyzing colors in: {image_path}")

    # Convert to HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # Sample colors from different parts of the image
    height, width = image.shape[:2]

    # Sample from center regions where cards might be
    sample_regions = [
        (width//4, height//4, width//2, height//2),      # Center
        (width//6, height//6, width//3, height//3),      # Upper left area
        (2*width//3, height//6, width//3, height//3),    # Upper right area
        (width//6, 2*height//3, width//3, height//3),    # Lower left area
        (2*width//3, 2*height//3, width//3, height//3),  # Lower right area
    ]

    all_colors = []
    for i, (x, y, w, h) in enumerate(sample_regions):
        region = hsv[y:y+h, x:x+w]

        # Get unique colors in this region
        region_colors = region.reshape(-1, 3)
        unique_colors = np.unique(region_colors, axis=0)

        # Filter for greenish colors (hue between 30-120)
        green_colors = unique_colors[(unique_colors[:, 0] >= 30) & (unique_colors[:, 0] <= 120)]

        if len(green_colors) > 0 and debugging:
            print(f"  Region {i+1}: Found {len(green_colors)} green-ish colors")
            # Show a few examples
            for j, color in enumerate(green_colors[:5]):
                print(f"    HSV: {color}")

        all_colors.extend(green_colors)

    if debugging and len(all_colors) > 0:
        print(f"📊 Total green-ish colors found: {len(all_colors)}")

        # Find most common green colors
        unique_all = np.unique(np.array(all_colors), axis=0)
        print(f"📈 Unique green colors: {len(unique_all)}")

        # Show some examples
        print("🎯 Sample green colors found:")
        for i, color in enumerate(unique_all[:10]):
            print(f"  {i+1}: HSV({color[0]}, {color[1]}, {color[2]})")


def test_all_images(debugging=True):
    """Test the color detection on all test images"""
    
    test_dir = "test images"
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return
    
    test_images = [f for f in os.listdir(test_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if debugging:
        print(f"🧪 Testing color-based detection on {len(test_images)} images...")
        print("=" * 60)
    
    for image_file in test_images:
        image_path = os.path.join(test_dir, image_file)
        print(f"\n📸 Testing: {image_file}")
        print("-" * 40)
        
        card_paths = crop_cards_by_color(image_path, debugging=debugging)
        
        if card_paths:
            print(f"✅ Success! Found {len(card_paths)} cards:")
            for path in card_paths:
                print(f"  📄 {path}")
        else:
            print("❌ No cards detected")
        
        print("-" * 40)

# Simple test function
def test_color_detection():
    """Simple test function for color-based card detection"""

    print("🎨 COLOR-BASED CARD DETECTION TEST")
    print("=" * 50)

    # Test on test4.jpg with debugging
    card_paths = crop_cards_by_color("test images/test2.jpg", debugging=True)

    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024  # Size in KB
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")

        print(f"\n📁 Cards saved to: color_cropped_cards/")
        print("🔍 These should include the complete cards with person photos!")

    else:
        print("❌ No cards detected")

    return card_paths

# Run tests
if __name__ == "__main__":
    test_color_detection()
