#!/usr/bin/env python3
"""
Test script for card detection and cropping.
"""

import os
from detect_and_crop_cards import crop_cards_from_image

def test_card_detection():
    """Test the card detection on test4.jpg"""
    
    # Path to test image
    test_image = os.path.join("test images", "test4.jpg")
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    print("Testing card detection and cropping...")
    print(f"Input image: {test_image}")
    
    try:
        # Run card detection and cropping
        saved_paths = crop_cards_from_image(test_image, "cropped_cards")
        
        if saved_paths:
            print(f"\n✅ Successfully detected and cropped {len(saved_paths)} cards!")
            print("\nOutput files:")
            for i, path in enumerate(saved_paths, 1):
                if os.path.exists(path):
                    file_size = os.path.getsize(path) / 1024  # Size in KB
                    print(f"  Card {i}: {path} ({file_size:.1f} KB)")
                else:
                    print(f"  Card {i}: {path} (FILE NOT FOUND)")
        else:
            print("❌ No cards were detected or cropped.")
            
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")

if __name__ == "__main__":
    test_card_detection()
