#!/usr/bin/env python3
"""
Test script for the expansion feature.
"""

import os
from simple_card_cropper import crop_cards

def test_expansion():
    """Test different expansion settings"""
    
    test_image = os.path.join("test images", "test4.jpg")
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    print("Testing card cropping with expansion...")
    
    # Test 1: Default expansion
    print("\n=== Test 1: Default expansion ===")
    card_paths = crop_cards(test_image, "test1_default")
    print(f"Result: {len(card_paths)} cards detected")
    
    # Test 2: High expansion to capture complete cards
    print("\n=== Test 2: High expansion for complete cards ===")
    card_paths = crop_cards(test_image, "test2_high_expansion",
                           expand_top=100,    # 100% more upward
                           expand_bottom=50,  # 50% more downward  
                           expand_left=50,    # 50% more to left
                           expand_right=50)   # 50% more to right
    print(f"Result: {len(card_paths)} cards detected")
    
    # Test 3: Very high top expansion to ensure we get the person's photo
    print("\n=== Test 3: Very high top expansion for person photo ===")
    card_paths = crop_cards(test_image, "test3_photo_capture",
                           expand_top=150,    # 150% more upward for photo
                           expand_bottom=30,  # 30% more downward
                           expand_left=30,    # 30% more to left
                           expand_right=30)   # 30% more to right
    print(f"Result: {len(card_paths)} cards detected")
    
    print("\n✅ All tests completed!")
    print("Check the output folders to see the different expansion results:")
    print("  - test1_default/")
    print("  - test2_high_expansion/")
    print("  - test3_photo_capture/")

if __name__ == "__main__":
    test_expansion()
